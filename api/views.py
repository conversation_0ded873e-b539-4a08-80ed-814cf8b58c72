from django.contrib.auth import get_user_model
from django.db.models import Q  # Sum, Count
from django_filters.rest_framework import DjangoFilterBackend

from convert_numbers import (
    english_to_persian,
    # persian_to_english,
)
from jdatetime import datetime
from natsort import natsorted
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.decorators import api_view, permission_classes  # action
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework import viewsets, permissions, status  # parsers

from base.models import (
    Bank,
    Category,
    Event,
    ModeOptions,
    Tag,
    Transaction,
)

from base.utils import (
    NAMES_OF_MONTHS__PERSIAN,
    filter_objects_by_months,
    get_years,
    get_event_objects,
    get_to_shows,
    get_transaction_objects,
    # split_into_year_month_day,
)

from .serializers import (
    BankSerializer,
    CategorySerializer,
    EventSerializer,
    ModeSerializer,
    PasswordChangeSerializer,
    TagSerializer,
    TransactionSerializer,
    UserSerializer,
)

from .filters import (
    EventFilter,
    TransactionFilter,
)

User = get_user_model()


class MyCustomPagination(PageNumberPagination):
    ## JUMP_5
    page_size = 1000

    ## to override the page_size set in JUMP_5
    ## and pass it as a request parameter
    page_size_query_param = 'page_size'  ## /api/tickets/?page=1&page_size=50

    ## max allowed value for page_size=... parameter.
    ## for example, the page_size in /api/tickets/?page_size=20000
    ## is invalid and will fall back to the value below
    max_page_size = 20_000


# class IsSuperUser(permissions.BasePermission):
#     ...


class CustomAuthToken(ObtainAuthToken):
    '''
    Custom token authentication endpoint.

    This view provides a way for clients to obtain an authentication token
    given the username and password of a user.

    Example request:
    POST /api/token/
    {
        "username": "robert",
        "password": "abc123"
    }

    Example response:
    {
        "token": "9944b09199c62bcf9418ad846dd0e4bbdfc6ee4b",
        "id": 1,
        "username": "robert",
        "is_superuser": true,
        "is_limited_admin": false
    }
    '''
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'id': user.id,
            'username': user.username,
            'is_superuser': user.is_superuser,
            'is_limited_admin': user.is_limited_admin,
        }, status=status.HTTP_200_OK)


# class UserViewSet(viewsets.ReadOnlyModelViewSet):
#     ...


class BankViewSet(viewsets.ModelViewSet):
    """ViewSet for Bank model"""
    pagination_class = MyCustomPagination
    queryset = Bank.active_objects.all()
    serializer_class = BankSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    search_fields = ['title']
    lookup_field = 'short_uuid'
    ordering_fields = ['id', 'title', 'created']


class CategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for Category model"""
    pagination_class = MyCustomPagination
    queryset = Category.active_objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    search_fields = ['title']
    lookup_field = 'short_uuid'
    ordering_fields = ['id', 'title', 'created']


class TagViewSet(viewsets.ModelViewSet):
    """ViewSet for Tag model"""
    pagination_class = MyCustomPagination
    queryset = Tag.active_objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    search_fields = ['title']
    lookup_field = 'short_uuid'
    ordering_fields = ['id', 'title', 'created']


class TransactionViewSet(viewsets.ModelViewSet):
    """ViewSet for Transaction model"""
    pagination_class = MyCustomPagination
    queryset = Transaction.active_objects.all()
    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = TransactionFilter
    search_fields = ['title']
    lookup_field = 'short_uuid'
    ordering_fields = ['id', 'amount', 'year', 'month', 'day', 'created']

    def get_queryset(self):
        queryset = super().get_queryset()

        ## filter by date range
        year_from  = self.request.query_params.get('year_from')
        year_to    = self.request.query_params.get('year_to')
        month_from = self.request.query_params.get('month_from')
        month_to   = self.request.query_params.get('month_to')

        if year_from:  queryset = queryset.filter(year__gte=year_from)
        if year_to:    queryset = queryset.filter(year__lte=year_to)
        if month_from: queryset = queryset.filter(month__gte=month_from)
        if month_to:   queryset = queryset.filter(month__lte=month_to)

        # Filter by amount range
        amount_min = self.request.query_params.get('amount_min')
        amount_max = self.request.query_params.get('amount_max')

        if amount_min:
            queryset = queryset.filter(amount__gte=amount_min)
        if amount_max:
            queryset = queryset.filter(amount__lte=amount_max)

        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        ## as date field is not present in models.py nor in serializers.py
        ## we use request.data.get('date', '')
        ## instead of serializer.validated_data.get('date', '')
        slashed_date = request.data.get('date', '')  ## 1404/12/30

        mode   = serializer.validated_data.get('mode')  ## I/E
        amount = serializer.validated_data.get('amount')
        title  = serializer.validated_data.get('title', '')
        author = request.user

        bank_obj     = serializer.validated_data.get('bank')  ## can be None
        category_obj = serializer.validated_data.get('category')
        tag_objs     = serializer.validated_data.get('tags')

        serializer.save(
            mode=mode,
            title=title,
            author=author,
            amount=amount,
            slashed_date=slashed_date,
            bank=bank_obj,
            category=category_obj,
            # tags=tags,
        )

        serializer.instance.tags.set(tag_objs)

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
        )

    def update(self, request, *args, **kwargs):
        transction_object = self.get_object()

        if transction_object not in get_transaction_objects(request):
            return Response(
                {'detail': 'تراکنش یافت نشد'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(transction_object, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        ## as date field is not present in models.py nor in serializers.py
        ## we use request.data.get('date', '')
        ## instead of serializer.validated_data.get('date', '')
        slashed_date = request.data.get('date', '')  ## 1404/12/30

        ## commented because not needed
        # mode   = serializer.validated_data.get('mode')  ## I/E
        # amount = serializer.validated_data.get('amount')
        # title  = serializer.validated_data.get('title', '')
        # author = request.user

        bank_obj     = serializer.validated_data.get('bank')  ## can be None
        category_obj = serializer.validated_data.get('category')
        tag_objs     = serializer.validated_data.get('tags')

        serializer.save(
            slashed_date=slashed_date,
            bank=bank_obj,
            category=category_obj,
            # tags=tags,
        )

        serializer.instance.tags.set(tag_objs)

        return Response(serializer.data, status=status.HTTP_200_OK)

class EventViewSet(viewsets.ModelViewSet):
    """ViewSet for Event model"""
    pagination_class = MyCustomPagination
    queryset = Event.active_objects.all()
    serializer_class = EventSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = EventFilter
    search_fields = ['title']
    lookup_field = 'short_uuid'
    ordering_fields = ['id', 'year', 'month', 'day', 'created']

    def get_queryset(self):
        queryset = super().get_queryset()

        ## filter by date range
        year_from  = self.request.query_params.get('year_from')
        year_to    = self.request.query_params.get('year_to')
        month_from = self.request.query_params.get('month_from')
        month_to   = self.request.query_params.get('month_to')

        if year_from:  queryset = queryset.filter(year__gte=year_from)
        if year_to:    queryset = queryset.filter(year__lte=year_to)
        if month_from: queryset = queryset.filter(month__gte=month_from)
        if month_to:   queryset = queryset.filter(month__lte=month_to)

        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        ## as date field is not present in models.py nor in serializers.py
        ## we use request.data.get('date', '')
        ## instead of serializer.validated_data.get('date', '')
        slashed_date = request.data.get('date', '')  ## 1404/12/30

        title = serializer.validated_data.get('title', '')
        author = request.user

        serializer.save(
            title=title,
            author=author,
            slashed_date=slashed_date,
        )

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
        )

    def update(self, request, *args, **kwargs):
        event_object = self.get_object()

        if event_object not in get_event_objects(request):
            return Response(
                {'detail': 'رویداد یافت نشد'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(event_object, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        ## as date field is not present in models.py nor in serializers.py
        ## we use request.data.get('date', '')
        ## instead of serializer.validated_data.get('date', '')
        slashed_date = request.data.get('date', '')  ## 1404/12/30

        ## commented because not needed
        # title  = serializer.validated_data.get('title', '')
        # author = request.user

        serializer.save(
            slashed_date=slashed_date,
        )

        return Response(serializer.data, status=status.HTTP_200_OK)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def reports(request):
    ## __TODO__
    return Response(
        {'detail': 'این صفحه به روز خواهد شد'},
        status=status.HTTP_200_OK,
    )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_homepage_statistics(request):
    '''
    API endpoint that returns homepage statistics.

    Query parameters:
    - year: (Optional) e.g. 1404 (default: current year)
    - month-start: (Optional) e.g. 9, 12, etc. (default: current month)
    - month-end: (Optional) e.g. 9, 12, etc. (default: 0)

    Example response:
    {
        "years": [1401, ...],
        "names_of_months__persian": ["فروردین", ...],
        "chosenyear": 1404,
        "chosenmonthstart": 3,
        "chosenmonthend": 0,
        "chosenmonthstart__zeroed": "03",
        "chosenmonthend__zeroed": "00",
        "chosenyear__persian": "۱۴۰۴",
        "chosenmonthstart__zeroed__persian": "۰۳",
        "chosenmonthend__zeroed__persian": "۰۰",
        "income_objects": [
            {
                "id": 1,
                "mode": "I",
                "title": "فروش کتاب",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "g3hd8de8"
                },
                "amount": 50000,
                "year": 1401,
                "month": 12,
                "day": 1,
                "bank_info": {
                    "id": 1,
                    "title": "بانک",
                    "short_uuid": "g3hd8de8"
                },
                "category_info": {
                    "id": 1,
                    "title": "متفرقه",
                    "short_uuid": "g3hd8de8"
                },
                "tags_names": [
                    "مبین‌نت"
                ],
                "short_uuid": "6b996016",
                "active": true,
                "created": "2023-10-10T08:06:36.280000",
                "updated": "2023-10-10T08:06:36.281000"
            },
            ...
        ],
        "expenditure_objects": [...],
        "event_objects": [
            {
                "id": 1,
                "title": "خرید گوشی موبایل",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "g3hd8de8"
                },
                "year": 1400,
                "month": 1,
                "day": 2,
                "active": true,
                "short_uuid": "98c27a3f",
                "created": "2023-10-10T08:06:39.886000",
                "updated": "2023-10-10T08:06:39.886000"
            },
            ...
        ]
    }
    '''

    chosenyear       = int(request.GET.get('year', 0))
    chosenmonthstart = int(request.GET.get('month-start', 0))
    chosenmonthend   = int(request.GET.get('month-end', 0))

    if not chosenyear:
        j_now = datetime.now()
        chosenyear = int(j_now.strftime('%Y'))

    if not chosenmonthstart:
        j_now = datetime.now()
        chosenmonthstart = int(j_now.strftime('%m'))

    ## commented because month-end is allowed to be left empty
    ## meaning no need to caluculate current month
    # if not chosenmonthend:
    #     j_now = datetime.now()
    #     chosenmonthend = int(j_now.strftime('%m'))

    if chosenmonthstart == chosenmonthend:
        chosenmonthend = 0
    elif chosenmonthend and chosenmonthstart > chosenmonthend:
        chosenmonthstart, chosenmonthend = chosenmonthend, chosenmonthstart

    ## ------------------------

    transaction_objects = get_transaction_objects(request)
    event_objects       = get_event_objects(request)

    ## JUMP_3
    income_objects_of_year      = transaction_objects.filter(mode=ModeOptions.INCOME,      year=chosenyear)
    expenditure_objects_of_year = transaction_objects.filter(mode=ModeOptions.EXPENDITURE, year=chosenyear)
    event_objects_of_year       = event_objects.filter(year=chosenyear)

    ## ------------------------

    chosenmonthstart__zeroed = f'{chosenmonthstart:02}'
    chosenmonthend__zeroed   = f'{chosenmonthend:02}'

    chosenyear__persian               = english_to_persian(chosenyear)                ## ۱۴۰۰
    chosenmonthstart__zeroed__persian = english_to_persian(chosenmonthstart__zeroed)  ## ۰۱
    chosenmonthend__zeroed__persian   = english_to_persian(chosenmonthend__zeroed)    ## ۰۰

    dic = {
        'years': get_years(),
        'names_of_months__persian': NAMES_OF_MONTHS__PERSIAN,

        'chosenyear':       chosenyear,
        'chosenmonthstart': chosenmonthstart,
        'chosenmonthend':   chosenmonthend,

        'chosenmonthstart__zeroed': chosenmonthstart__zeroed,
        'chosenmonthend__zeroed':   chosenmonthend__zeroed,

        'chosenyear__persian':               chosenyear__persian,
        'chosenmonthstart__zeroed__persian': chosenmonthstart__zeroed__persian,
        'chosenmonthend__zeroed__persian':   chosenmonthend__zeroed__persian,

        'income_objects': TransactionSerializer(
            natsorted(
                filter_objects_by_months(income_objects_of_year, chosenmonthstart, chosenmonthend),
                key=lambda k: k.numerical_date
            ),
            many=True
        ).data,

        'expenditure_objects': TransactionSerializer(
            natsorted(
                filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend),
                key=lambda k: k.numerical_date
            ),
            many=True
        ).data,

        'event_objects': EventSerializer(
            natsorted(
                filter_objects_by_months(event_objects_of_year, chosenmonthstart, chosenmonthend),
                key=lambda k: k.numerical_date
            ),
            many=True
        ).data,
    }

    return Response(
        dic,
        status=status.HTTP_200_OK,
    )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def mode_list(request):
    '''
    API endpoint that returns all available mode options.

    Example response:
    [
        {
            "value": "I",
            "label": "درآمد"
        },
        {
            "value": "E",
            "label": "هزینه"
        }
    ]
    '''
    modes = [
        {'value': choice[0], 'label': choice[1]}
        for choice in ModeOptions.choices
    ]
    serializer = ModeSerializer(modes, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def profile(request):
    '''
    API endpoint that returns the current user's profile.

    Example response:
    {
        "id": 9,
        "username": "sara",
        "first_name": "سارا",
        "last_name": "سروشی",
        "email": "<EMAIL>",
        "is_superuser": false,
        "is_limited_admin": false,
        "company": null,
        "position": null,
        "description": null,
        "gender": "M",
        "short_uuid": "55eed745"
    }
    '''

    ## NOTE unlike profile function in accounts/views.py,
    ##      here everyone sees their own profile only

    serializer = UserSerializer(request.user)
    return Response(
        serializer.data,
        status=status.HTTP_200_OK
    )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def change_password(request):
    '''
    API endpoint that allows users to change their password.

    This endpoint requires the user to provide their current password
    and a new password (entered twice for confirmation).

    Example request:
    POST /api/change-password/
    {
        "old_password": "current-password",
        "new_password1": "new-password",
        "new_password2": "new-password"
    }
    '''
    serializer = PasswordChangeSerializer(
        data=request.data,
        context={'request': request},
    )

    if serializer.is_valid():
        user = request.user

        ## set the new password
        user.set_password(serializer.validated_data['new_password1'])
        user.save()

        ## update the user's auth token to invalidate old sessions
        Token.objects.filter(user=user).delete()
        Token.objects.create(user=user)

        return Response(
            {'detail': 'رمز عبور تغییر یافت'},
            status=status.HTTP_200_OK
        )

    return Response(
        serializer.errors,
        status=status.HTTP_400_BAD_REQUEST,
    )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search(request):
    '''
    API endpoint that allows users to search across transactions and events.

    The search is performed on titles of these items.

    Example request:
    GET /api/search/?q=keyword

    Example response:
    {
        "matched_transactions": [
            {
                "id": 146,
                "mode": "E",
                "title": "شارژ ایرانسل",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "53h3ab1c"
                },
                "amount": 21800,
                "year": 1402,
                "month": 5,
                "day": 10,
                "slashed_date": "1402/05/10",
                "slashed_date_persian": "۱۴۰۲/۰۵/۱۰",
                "numerical_date": "********",
                "bank_info": {
                    "id": 20,
                    "title": "ملت",
                    "short_uuid": "4694b2fr"
                },
                "category_info": {
                    "id": 20,
                    "title": "متفرقه",
                    "short_uuid": "d5e03393"
                },
                "tags_names": [],
                "short_uuid": "22451b45",
                "active": true,
                "created": "2023-10-10T08:06:21.765000",
                "updated": "2023-10-10T08:06:21.765000"
            },
            ...
        ],
        "matched_events": [
            {
                "id": 212,
                "title": "تماس با مشتری",
                "author": {
                    "id": 1,
                    "username": "robert",
                    "is_superuser": true,
                    "is_limited_admin": false,
                    "short_uuid": "53boab1c"
                },
                "year": 1402,
                "month": 10,
                "day": 30,
                "slashed_date": "1402/10/30",
                "slashed_date_persian": "۱۴۰۲/۱۰/۳۰",
                "numerical_date": "14021030",
                "active": true,
                "short_uuid": "2bx7148c",
                "created": "2024-01-21T19:31:39.671000",
                "updated": "2024-01-21T19:31:39.671000"
            },
            ...
        ],
    }
    '''
    query = get_to_shows(
        request,
        'q',
    )

    matched_transactions = []
    matched_events       = []

    if query:
        try:
            query_int = int(query)  ## 45000
        except ValueError:
            query_int = None

        ## transaction ---------------

        trns_q_objects = Q(short_uuid=query) | \
            Q(title__icontains=query) | \
            Q(slashed_date__icontains=query) | \
            Q(slashed_date_persian__icontains=query) | \
            Q(bank__title__icontains=query) | \
            Q(category__title__icontains=query) | \
            Q(tags__title__icontains=query)

        ## include more fields in search if query is an int
        if query_int is not None:
            trns_q_objects |= Q(amount=query_int) | \
                Q(year=query_int) | \
                Q(month=query_int) | \
                Q(day=query_int) | \
                Q(numerical_date=query_int)

        matched_transactions = get_transaction_objects(request).filter(trns_q_objects).distinct()

        ## event ---------------

        event_q_objects = Q(short_uuid=query) | \
            Q(title__icontains=query) | \
            Q(slashed_date__icontains=query) | \
            Q(slashed_date_persian__icontains=query)

        ## include more fields in search if query is an int
        if query_int is not None:
            event_q_objects |= Q(year=query_int) | \
                Q(month=query_int) | \
                Q(day=query_int) | \
                Q(numerical_date=query_int)

        matched_events = get_event_objects(request).filter(event_q_objects).distinct()

    if all([
        not matched_transactions,
        not matched_events,
    ]):
        return Response(
            {'detail': 'نتیجه‌ای یافت نشد'},
            status=status.HTTP_404_NOT_FOUND
        )

    context = {'request': request}
    return Response(
        {
            'matched_transactions': TransactionSerializer(matched_transactions, many=True, context=context).data,
            'matched_events': EventSerializer(matched_events, many=True, context=context).data,
            'queried_string': query,
        },
        status=status.HTTP_200_OK
    )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout_view(request):
    '''Logout user by deleting their token'''
    try:
        request.user.auth_token.delete()
        return Response(
            {'detail': 'خروج موفق'},
            status=status.HTTP_200_OK,
        )
    except:
        return Response(
            {'detail': 'خطا در خروج'},
            status=status.HTTP_400_BAD_REQUEST,
        )
