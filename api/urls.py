from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from . import views


## create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'banks',        views.BankViewSet,        basename='bank')
router.register(r'categories',   views.CategoryViewSet,    basename='category')
router.register(r'events',       views.EventViewSet,       basename='event')
router.register(r'tags',         views.TagViewSet,         basename='tag')
router.register(r'transactions', views.TransactionViewSet, basename='transaction')
# router.register(r'users',        views.UserViewSet,        basename='user')

urlpatterns = [
    path('', include(router.urls)),

    path('get-homepage-statistics/', views.get_homepage_statistics, name='api-get-homepage-statistics-url'),
    path('modes/',                   views.mode_list,               name='api-modes-url'),
    path('reports/',                 views.reports,                 name='api-reports-url'),
    path('search/',                  views.search,                  name='api-search-url'),

    ## user
    path('change-password/', views.change_password, name='api-change-password-url'),
    path('profile/',         views.profile,         name='api-profile-url'),

    ## authentication
    path('token/',  views.CustomAuthToken.as_view(), name='api-token-url'),
    path('logout/', views.logout_view,               name='api-logout-url'),

    ## browsable api login/logout
    path('auth/', include('rest_framework.urls', namespace='rest_framework')),
]
