# API Documentation

<div align="center" id="top">
  <p style="margin:0">Endpoints</p>
  <p>
    <a href="#authentication-endpoints">Authentication</a> •
    <a href="#user-endpoints">User</a> •
    <a href="#bank-endpoints">Bank</a> •
    <a href="#category-endpoints">Category</a> •
    <a href="#tag-endpoints">Tag</a> •
    <a href="#transaction-endpoints">Transaction</a> •
    <a href="#event-endpoints">Event</a> •
    <a href="#search-endpoint">Search</a> •
    <a href="#report-endpoints">Report</a>
  </p>
</div>

## Overview

A comprehensive Django REST API for financial management, supporting transactions, events, banks, categories, and user management.

## Endpoints

### Authentication Endpoints

- `POST /api/token/` obtain a token
- `POST /api/logout/` logout

### Obtain a Token
```sh
POST /api/token/
{
  "username": "robert",
  "password": "123pass"
}

## shell command
curl -X POST \
  $base_url/api/token/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "robert",
    "password": "123pass"
  }'

## the response will include a token
## that should be included in the Authorization header
## of all subsequent requests:
curl -X POST \
  $base_url/.../ \
  -H "Authorization: Token f10e76cc5f1a07820d75dd2e45ffea8a8019f175" \
  ...
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### User Endpoints

- `GET /api/profile/` - Get the current user's profile
- `POST /api/change-password/` - Change the current user's password

### Get Profile
```sh
GET /api/profile/

## shell command
curl -X GET \
  $base_url/api/profile/ \
  -H "Authorization: Token $token"
```

### Change Password
```sh
POST /api/change-password/
{
  "old_password": "current-password",
  "new_password1": "new-password",
  "new_password2": "new-password"
}

## shell command
curl -X POST \
  $base_url/api/change-password/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "current-password",
    "new_password1": "new-password",
    "new_password2": "new-password"
  }'
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Bank Endpoints

- `GET /api/banks/` - List all banks
- `GET /api/banks/{short_uuid}/` - Get a specific bank
- `POST /api/banks/` - Create a new bank
- `PUT /api/banks/{short_uuid}/` - Update a bank
- `PATCH /api/banks/{short_uuid}/` - Partially update a bank
- `DELETE /api/banks/{short_uuid}/` - Delete a bank

### Get Banks
```sh
GET /api/banks/

## shell command
curl -X GET \
  $base_url/api/banks/ \
  -H "Authorization: Token $token"
```

### Get a Bank
```sh
GET /api/banks/{short_uuid}/

## shell command
curl -X GET \
  $base_url/api/banks/$short_uuid/ \
  -H "Authorization: Token $token"
```

### Create a Bank
```sh
POST /api/banks/
{
  "title": "This is title"
}

## shell command
curl -X POST \
  $base_url/api/banks/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "This is title"
  }'
```

### Update a Bank
```sh
PUT /api/banks/{short_uuid}/
{
  "title": "Updated title"
}

## shell command
curl -X PUT \
  $base_url/api/banks/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated title"
  }'
```

### Partially Update a Bank
```sh
PATCH /api/banks/{short_uuid}/
{
  "title": "Partially updated title"
}

## shell command
curl -X PATCH \
  $base_url/api/banks/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Partially updated title"
  }'
```

### Delete a Bank
```sh
DELETE /api/banks/{short_uuid}/

## shell command
curl -X DELETE \
  $base_url/api/banks/$short_uuid/ \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Category Endpoints

- `GET /api/categories/` - List all categories
- `GET /api/categories/{short_uuid}/` - Get a specific category
- `POST /api/categories/` - Create a new category
- `PUT /api/categories/{short_uuid}/` - Update a category
- `PATCH /api/categories/{short_uuid}/` - Partially update a category
- `DELETE /api/categories/{short_uuid}/` - Delete a category

### Get Categories
```sh
GET /api/categories/

## shell command
curl -X GET \
  $base_url/api/categories/ \
  -H "Authorization: Token $token"
```

### Get a Category
```sh
GET /api/categories/{short_uuid}/

## shell command
curl -X GET \
  $base_url/api/categories/$short_uuid/ \
  -H "Authorization: Token $token"
```

### Create a Category
```sh
POST /api/categories/
{
  "title": "This is title"
}

## shell command
curl -X POST \
  $base_url/api/categories/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "This is title"
  }'
```

### Update a Category
```sh
PUT /api/categories/{short_uuid}/
{
  "title": "Updated title"
}

## shell command
curl -X PUT \
  $base_url/api/categories/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated title"
  }'
```

### Partially Update a Category
```sh
PATCH /api/categories/{short_uuid}/
{
  "title": "Partially updated title"
}

## shell command
curl -X PATCH \
  $base_url/api/categories/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Partially updated title"
  }'
```

### Delete a Category
```sh
DELETE /api/categories/{short_uuid}/

## shell command
curl -X DELETE \
  $base_url/api/categories/$short_uuid/ \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Tag Endpoints

- `GET /api/tags/` - List all tags
- `GET /api/tags/{short_uuid}/` - Get a specific tag
- `POST /api/tags/` - Create a new tag
- `PUT /api/tags/{short_uuid}/` - Update a tag
- `PATCH /api/tags/{short_uuid}/` - Partially update a tag
- `DELETE /api/tags/{short_uuid}/` - Delete a tag

### Get Tags
```sh
GET /api/tags/

## shell command
curl -X GET \
  $base_url/api/tags/ \
  -H "Authorization: Token $token"
```

### Get a Tag
```sh
GET /api/tags/{short_uuid}/

## shell command
curl -X GET \
  $base_url/api/tags/$short_uuid/ \
  -H "Authorization: Token $token"
```

### Create a Tag
```sh
POST /api/tags/
{
  "title": "This is title"
}

## shell command
curl -X POST \
  $base_url/api/tags/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "This is title"
  }'
```

### Update a Tag
```sh
PUT /api/tags/{short_uuid}/
{
  "title": "Updated title"
}

## shell command
curl -X PUT \
  $base_url/api/tags/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated title"
  }'
```

### Partially Update a Tag
```sh
PATCH /api/tags/{short_uuid}/
{
  "title": "Partially updated title"
}

## shell command
curl -X PATCH \
  $base_url/api/tags/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Partially updated title"
  }'
```

### Delete a Tag
```sh
DELETE /api/tags/{short_uuid}/

## shell command
curl -X DELETE \
  $base_url/api/tags/$short_uuid/ \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Transaction Endpoints

- `GET /api/transactions/` - List all transactions
- `GET /api/transactions/{short_uuid}/` - Get a specific transaction
- `POST /api/transactions/` - Create a new transaction
- `PUT /api/transactions/{short_uuid}/` - Update a transaction
- `PATCH /api/transactions/{short_uuid}/` - Partially update a transaction
- `DELETE /api/transactions/{short_uuid}/` - Delete a transaction

### Get Transactions
```sh
GET /api/transactions/

## shell command
curl -X GET \
  $base_url/api/transactions/ \
  -H "Authorization: Token $token"
```

### Get a Transaction
```sh
GET /api/transactions/{short_uuid}/

## shell command
curl -X GET \
  $base_url/api/transactions/$short_uuid/ \
  -H "Authorization: Token $token"
```

### Create a Transaction
```sh
POST /api/transactions/
{
  "title": "This is title",
  "amount": "123456",
  "date": "1404/11/30",
  "bank": "1",
  "tags": [1, 2],
  "category": "1"
}

## shell command
curl -X POST \
  $base_url/api/transactions/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "This is title",
    "amount": "123456",
    "date": "1404/11/30",
    "bank": "1",
    "tags": [1, 2],
    "category": "1"
  }'
```

### Update a Transaction
```sh
PUT /api/transactions/{short_uuid}/
{
  "title": "Updated title"
}

## shell command
curl -X PUT \
  $base_url/api/transactions/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated title"
  }'
```

### Partially Update a Transaction
```sh
PATCH /api/transactions/{short_uuid}/
{
  "title": "Partially updated title"
}

## shell command
curl -X PATCH \
  $base_url/api/transactions/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Partially updated title"
  }'
```

### Delete a Transaction
```sh
DELETE /api/transactions/{short_uuid}/

## shell command
curl -X DELETE \
  $base_url/api/transactions/$short_uuid/ \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Event Endpoints

- `GET /api/events/` - List all events
- `GET /api/events/{short_uuid}/` - Get a specific events
- `POST /api/events/` - Create a new events
- `PUT /api/events/{short_uuid}/` - Update a events
- `PATCH /api/events/{short_uuid}/` - Partially update a events
- `DELETE /api/events/{short_uuid}/` - Delete a events

### Get Events
```sh
GET /api/events/

## shell command
curl -X GET \
  $base_url/api/events/ \
  -H "Authorization: Token $token"
```

### Get an Event
```sh
GET /api/events/{short_uuid}/

## shell command
curl -X GET \
  $base_url/api/events/$short_uuid/ \
  -H "Authorization: Token $token"
```

### Create an Event
```sh
POST /api/events/
{
  "title": "This is title",
  "date": "1404/11/30"
}

## shell command
curl -X POST \
  $base_url/api/events/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "This is title"
  }'
```

### Update an Event
```sh
PUT /api/events/{short_uuid}/
{
  "title": "Updated title"
}

## shell command
curl -X PUT \
  $base_url/api/events/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated title"
  }'
```

### Partially Update an Event
```sh
PATCH /api/events/{short_uuid}/
{
  "title": "Partially updated title"
}

## shell command
curl -X PATCH \
  $base_url/api/events/$short_uuid/ \
  -H "Authorization: Token $token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Partially updated title"
  }'
```

### Delete an Event
```sh
DELETE /api/events/{short_uuid}/

## shell command
curl -X DELETE \
  $base_url/api/events/$short_uuid/ \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Search Endpoint

- `GET /api/search/?q={query}` - Search across transactions and events

### Search
```sh
GET /api/search/?q={query}

## shell command
curl -X GET \
  $base_url/api/search/?q=برای \
  -H "Authorization: Token $token"
```

<div style="margin:40px 0px; display: flex; align-items: center;">
  <a href="#top" style="margin-right: 8px;">TOP</a>
  <div style="flex-grow: 1; border-top: 1px dashed gray;"></div>
</div>

### Report Endpoints

## __TODO__
