from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, <PERSON>, <PERSON>, Avg, IntegerField, Q
from django.http import JsonResponse, FileResponse, Http404  # HttpResponse
from django.shortcuts import render, redirect

from convert_numbers import english_to_persian
from jdatetime import datetime
from json import loads
from natsort import natsorted
from rahavard import (
    clear_messages,
    comes_from_htmx,
    convert_timestamp_to_jalali,
)

import httpx

import os

from .utils import (
    INDEXES_OF_MONTHS,
    NAMES_OF_MONTHS__PERSIAN,

    fahrenheit_to_celsius,
    get_to_shows,
)

from .models import (
    Bank,
    Event,
    Category,
    ModeOptions,
    Tag,
    Transaction,
)

from .forms import (
    EventForm,
    TransactionForm,
)

from .utils import (
    filter_objects_by_months,
    get_event_objects,
    get_transaction_objects,
    get_years,
    # split_into_year_month_day,
)

from .handlers import (
    bad_request,
    page_not_found,
)

APP_TITLE = 'Base'
APP_SLUG  = 'base'


def globals(request):
    project_dict = {
        'project_title_persian': settings.PROJECT_TITLE_PERSIAN,
        'project_title':         settings.PROJECT_TITLE,
        'project_credit':        settings.PROJECT_CREDIT,
        'project_credit_url':    settings.PROJECT_CREDIT_URL,
        'project_start_year':    settings.PROJECT_START_YEAR,
    }

    if not request.user.is_authenticated:
        ## needed on login page
        return {
            **project_dict,
        }

    if comes_from_htmx(request):
        return {}

    return {
        **project_dict,
    }

@login_required
def homepage(request):
    ## JUMP_2

    chosenyear, \
    chosenmonthstart, \
    chosenmonthend = get_to_shows(
        request,
        'year',
        'month-start',
        'month-end',
    )

    if chosenmonthstart == chosenmonthend:
        chosenmonthend = 0
    elif chosenmonthend and chosenmonthstart > chosenmonthend:
        chosenmonthstart, chosenmonthend = chosenmonthend, chosenmonthstart

    ## ------------------------

    transaction_objects = get_transaction_objects(request)
    event_objects       = get_event_objects(request)

    ## JUMP_3
    income_objects_of_year      = transaction_objects.filter(mode=ModeOptions.INCOME,      year=chosenyear)
    expenditure_objects_of_year = transaction_objects.filter(mode=ModeOptions.EXPENDITURE, year=chosenyear)
    event_objects_of_year       = event_objects.filter(year=chosenyear)

    ## ------------------------

    chosenmonthstart__zeroed = f'{chosenmonthstart:02}'
    chosenmonthend__zeroed   = f'{chosenmonthend:02}'

    chosenyear__persian               = english_to_persian(chosenyear)                ## ۱۴۰۰
    chosenmonthstart__zeroed__persian = english_to_persian(chosenmonthstart__zeroed)  ## ۰۱
    chosenmonthend__zeroed__persian   = english_to_persian(chosenmonthend__zeroed)    ## ۰۰

    return render(
        request,
        f'{APP_SLUG}/homepage.html',
        context={
            'main_title': 'خانه',
            'years': get_years(),
            'names_of_months__persian': NAMES_OF_MONTHS__PERSIAN,

            'chosenyear':       chosenyear,
            'chosenmonthstart': chosenmonthstart,
            'chosenmonthend':   chosenmonthend,

            'chosenmonthstart__zeroed': chosenmonthstart__zeroed,
            'chosenmonthend__zeroed':   chosenmonthend__zeroed,

            'chosenyear__persian':               chosenyear__persian,
            'chosenmonthstart__zeroed__persian': chosenmonthstart__zeroed__persian,
            'chosenmonthend__zeroed__persian':   chosenmonthend__zeroed__persian,

            'income_objects':      natsorted(filter_objects_by_months(income_objects_of_year,      chosenmonthstart, chosenmonthend), key=lambda k: k.numerical_date),  ## JUMP_1 sort based on property
            'expenditure_objects': natsorted(filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend), key=lambda k: k.numerical_date),  ## JUMP_1
            'event_objects':       natsorted(filter_objects_by_months(event_objects_of_year,       chosenmonthstart, chosenmonthend), key=lambda k: k.numerical_date),  ## JUMP_1
        },
    )

@login_required
def add(request, mdl):
    mdl = mdl.lower()
    if mdl == 'transaction':
        mdl_persian = 'تراکنش'
    elif mdl == 'event':
        mdl_persian = 'رویداد'

    if request.method == 'POST':
        if   mdl == 'transaction': form = TransactionForm(data=request.POST)
        elif mdl == 'event':       form = EventForm(data=request.POST)

        # form.data = <QueryDict: {
        #     'csrfmiddlewaretoken': ['3zQGnzwbzOJURCyrlXsvfMaIY0DvbHPFuZT5X01Whgwe2ZGQXMwhyXTeT57CdgGz'],
        #     'date': ['1401/1/16'],
        #     'title': ['mobin'],
        #     'amount': ['1000'],
        #     'from_transaction_form': ['Save']
        # }>

        if form.is_valid():
            from_transaction_form = request.POST.get('from_transaction_form')
            from_event_form       = request.POST.get('from_event_form')

            title = form.cleaned_data.get('title')

            slashed_date = request.POST.get('date', '')  ## 1404/12/30

            if from_transaction_form:
                mode   = form.cleaned_data.get('mode')  ## I/E
                amount = form.cleaned_data.get('amount')

                category     = request.POST.get('category')  ## 2
                category_obj = Category.active_objects.filter(id=category).first()  ## <Category: شخصی>

                ## 'or 0' because user is allowed to leave bank field empty
                ## (setting default to 0 did not work)
                bank     = request.POST.get('bank') or 0  ## 2
                bank_obj = Bank.active_objects.filter(id=bank).first()  ## <Bank: ملی>

                tags     = request.POST.getlist('tags')  ## ['3', '5']
                tag_objs = Tag.active_objects.filter(id__in=tags)

                new_obj = Transaction.objects.create(
                    mode=mode,
                    title=title,
                    author=request.user,
                    amount=amount,
                    slashed_date=slashed_date,
                    bank=bank_obj,
                    category=category_obj,
                    # tags=tags,
                )
                new_obj.tags.set(tag_objs)
                new_obj.save()
            elif from_event_form:
                new_obj = Event.objects.create(
                    title=title,
                    author=request.user,
                    slashed_date=slashed_date,
                )
                new_obj.save()
            clear_messages(request)
            messages.success(request, f'{mdl_persian} با شناسه {new_obj.short_uuid} ثبت شد')
        else:
            clear_messages(request)
            messages.add_message(request, messages.ERROR, f'{mdl_persian} ثبت نشد')
            messages.add_message(request, messages.ERROR, form.errors)

    if   mdl == 'transaction': form = TransactionForm()
    elif mdl == 'event':       form = EventForm()

    return render(
        request,
        f'{APP_SLUG}/add.html',
        context={
            'main_title': f'افزودن {mdl_persian}',
            'mdl': mdl,
            'form': form,
            'is_creating': True,
        },
    )

@login_required
def edit(request, mdl, shortuuid):
    mdl = mdl.lower()
    if mdl == 'transaction':
        mdl_persian = 'تراکنش'
        objects = get_transaction_objects(request)
    elif mdl == 'event':
        mdl_persian = 'رویداد'
        objects = get_event_objects(request)

    obj_to_edit = objects.filter(short_uuid=shortuuid).first()

    if not obj_to_edit:
        return page_not_found(request, error_msg=f'{mdl_persian} با شناسه {shortuuid} پیدا نشد')

    ## NOTE nearly the same structure repeated in create function
    if request.method == 'POST':
        if   mdl == 'transaction': form = TransactionForm(data=request.POST, instance=obj_to_edit)
        elif mdl == 'event':       form = EventForm(data=request.POST, instance=obj_to_edit)

        ## NOTE JUMP_1 in this application,
        ##             we exceptionally use this if statement for post requests
        ##             because there are post requests sent by selects on sidebar
        ##             with the ids of {user,category,priority}-id-select in main.html
        ##             these post requests get problematic when there are already other forms on page
        ##             e.g. for creating/replying to ticket, changing password, etc.
        ##             so in this app, we add this if statement to each post request
        ##             to make sure the request is actually coming from the form on the page
        ##             and not from the selects on the sidebar
        if form.is_valid():
            from_transaction_edit_form = request.POST.get('from_transaction_edit_form')
            from_event_edit_form       = request.POST.get('from_event_edit_form')
            title = form.cleaned_data.get('title')

            slashed_date = request.POST.get('date', '')  ## 1404/12/30

            if from_transaction_edit_form:
                mode   = form.cleaned_data.get('mode')  ## I/E
                amount = form.cleaned_data.get('amount')

                category     = request.POST.get('category')  ## 2
                category_obj = Category.active_objects.filter(id=category).first()  ## <Category: شخصی>

                bank     = request.POST.get('bank')  ## 2
                bank_obj = Bank.active_objects.filter(id=bank).first()  ## <Bank: ملی>

                tags     = request.POST.getlist('tags')  ## ['3', '5']
                tag_objs = Tag.active_objects.filter(id__in=tags)

                edited_object = form.save(commit=False)

                edited_object.mode     = mode
                edited_object.title    = title
                edited_object.author   = request.user
                edited_object.amount   = amount
                edited_object.slashed_date = slashed_date
                edited_object.bank     = bank_obj
                edited_object.category = category_obj

                edited_object.tags.set(tag_objs)
                edited_object.save()
            elif from_event_edit_form:
                edited_object = form.save(commit=False)
                edited_object.title  = title
                edited_object.author = request.user
                edited_object.slashed_date = slashed_date

                edited_object.save()
            clear_messages(request)
            messages.success(request, f'ویرایش {mdl_persian} با شناسه {shortuuid} انجام شد')
            return redirect('base-homepage-url')

        else:
            clear_messages(request)
            messages.add_message(request, messages.ERROR, f'ویرایش {mdl_persian} با شناسه {shortuuid} انجام نشد:')
            messages.add_message(request, messages.ERROR, form.errors)
            return redirect('base-homepage-url')

    if   mdl == 'transaction': form = TransactionForm(instance=obj_to_edit)
    elif mdl == 'event':       form = EventForm(instance=obj_to_edit)

    return render(
        request,
        f'{APP_SLUG}/add.html',  ## used the readily available created.html instead of creating an edit.html
        context={
            'main_title': f'ویرایش {mdl_persian}',
            'form': form,
            'shortuuid_to_edit': shortuuid,
            'obj_to_edit': obj_to_edit,
            'mdl': mdl,
            'is_editing': True,
        }
    )

@login_required
def delete(request, mdl, shortuuid):
    mdl = mdl.lower()
    if mdl == 'transaction':
        mdl_persian = 'تراکنش'
        objects = get_transaction_objects(request)
    elif mdl == 'event':
        mdl_persian = 'رویداد'
        objects = get_event_objects(request)

    obj_to_delete = objects.filter(short_uuid=shortuuid).first()

    if not obj_to_delete:
        return page_not_found(request, error_msg=f'{mdl_persian} با شناسه {shortuuid} پیدا نشد')

    obj_to_delete.delete()
    clear_messages(request)
    messages.success(request, f'{mdl_persian} با شناسه {shortuuid} حذف شد')

    return redirect('base-homepage-url')

@login_required
def weather(request):
    location = settings.VISUALCROSSING_LOCATION
    key      = settings.VISUALCROSSING_API_KEY
    include  = 'days'
    elements = 'datetime,datetimeEpoch,tempmax,tempmin,temp'

    params = f'?key={key}&include={include}&elements={elements}'

    url = f'https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline/{location}{params}'

    jalali_ymds = []
    maxes = []
    mins = []
    avgs = []

    ## using try because it may sometimes throw ConnectTimeoutError
    try:
        with httpx.Client() as client:
            response = client.get(
                url,
                timeout=5,
            )
            response.raise_for_status()

        d = loads(response.text)
        days = d.get('days', [])

        epochs = [_.get('datetimeEpoch', '') for _ in days]
        ## ['1735763400', '1735849800', ...]

        jalali_ymds = [
            convert_timestamp_to_jalali(_).split()[2]
            for _ in epochs
        ]
        ## ['۱۴۰۳/۱۰/۱۳', '۱۴۰۳/۱۰/۱۴', ...]

        maxes = [f"{fahrenheit_to_celsius(_.get('tempmax', None)):.2f}" for _ in days]
        mins  = [f"{fahrenheit_to_celsius(_.get('tempmin', None)):.2f}" for _ in days]
        avgs  = [f"{fahrenheit_to_celsius(_.get('temp',    None)):.2f}" for _ in days]

    # except httpx.HTTPStatusError as exc:
    #     response_status_code = exc.response.status_code
    #     pass

    except Exception:
        pass

    return JsonResponse(
        data={
            'labels': jalali_ymds,
            'maxes': maxes,
            'mins': mins,
            'avgs': avgs,
        }
    )

@login_required
def reports(request):
    '''
    category_objects_and_counts = {}
    tag_objects__and_counts = {}
    for c in Category.active_objects.all():
        ex_objs = expenditure_objects.filter(category__id=c.id)
        in_objs = income_objects.filter(category__id=c.id)
        category_objects_and_counts[c.title] = ex_objs.count() + in_objs.count()

    for t in Tag.active_objects.all():
        ex_objs = expenditure_objects.filter(tags__id=t.id)
        in_objs = income_objects.filter(tags__id=t.id)
        tag_objects__and_counts[t.title] = ex_objs.count() + in_objs.count()

    category_objects_and_counts = sort_dict(category_objects_and_counts, based_on='value', reverse=True)
    ## {'Groceries': 243, 'Internet': 42, 'Tuition': 31, ...}
    tag_objects__and_counts = sort_dict(tag_objects__and_counts, based_on='value', reverse=True)
    '''

    ## --------------------------------

    ## JUMP_2

    chosenyear, \
    chosenmonthstart, \
    chosenmonthend = get_to_shows(
        request,
        'year',
        'month-start',
        'month-end',
    )

    j_now = datetime.now()
    if not chosenyear:        chosenyear        = int(j_now.strftime('%Y'))
    if not chosenmonthstart: chosenmonthstart = int(j_now.strftime('%m'))

    if chosenmonthstart == chosenmonthend:
        chosenmonthend = 0
    elif chosenmonthend and chosenmonthstart > chosenmonthend:
        chosenmonthstart, chosenmonthend = chosenmonthend, chosenmonthstart

    ## --------------------------------

    transaction_objects = get_transaction_objects(request)

    ## JUMP_3
    income_objects_of_year      = transaction_objects.filter(mode=ModeOptions.INCOME,      year=chosenyear)
    expenditure_objects_of_year = transaction_objects.filter(mode=ModeOptions.EXPENDITURE, year=chosenyear)

    ## --------------------------------

    chosenmonthstart__zeroed = f'{chosenmonthstart:02}'
    chosenmonthend__zeroed   = f'{chosenmonthend:02}'

    chosenyear__persian                = english_to_persian(chosenyear)                 ## ۱۴۰۰
    chosenmonthstart__zeroed__persian = english_to_persian(chosenmonthstart__zeroed)  ## ۰۱
    chosenmonthend__zeroed__persian   = english_to_persian(chosenmonthend__zeroed)    ## ۰۰

    ## --------------------------------

    chart_type = get_to_shows(
        request,
        'chart-type',
    )
    if chart_type:
        if chart_type == 'monthly-sum':
            incomes__sum      = []
            expenditures__sum = []

            for m in INDEXES_OF_MONTHS:
                income_for_month = income_objects_of_year.filter(month=m).aggregate(
                    Sum=Sum('amount', default=0),
                )
                incomes__sum.append(income_for_month.get('Sum'))

                expenditure_for_month = expenditure_objects_of_year.filter(month=m).aggregate(
                    Sum=Sum('amount', default=0),
                    Avg=Avg('amount', output_field=IntegerField(), default=0),
                )
                expenditures__sum.append(expenditure_for_month.get('Sum'))

            return JsonResponse(
                data={
                    'labels': NAMES_OF_MONTHS__PERSIAN,
                    'incomes__sum':      incomes__sum,
                    'expenditures__sum': expenditures__sum,
                })

        elif chart_type == 'bank-sum':
            incomes__sum      = []
            expenditures__sum = []

            bank_objects = Bank.active_objects.all()
            titles_of_banks = list(bank_objects.values_list('title', flat=True))

            for bank_tit in titles_of_banks:
                income_for_cat = filter_objects_by_months(income_objects_of_year, chosenmonthstart, chosenmonthend).filter(bank__title=bank_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                incomes__sum.append(income_for_cat.get('Sum'))

                expenditure_for_cat = filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend).filter(bank__title=bank_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                expenditures__sum.append(expenditure_for_cat.get('Sum'))
            return JsonResponse(
                data={
                    'labels': titles_of_banks,
                    'incomes__sum': incomes__sum,
                    'expenditures__sum': expenditures__sum,
                })

        elif chart_type == 'category-sum':
            incomes__sum      = []
            expenditures__sum = []

            category_objects = Category.active_objects.all()
            titles_of_categories = list(category_objects.values_list('title', flat=True))

            for cat_tit in titles_of_categories:
                income_for_cat = filter_objects_by_months(income_objects_of_year, chosenmonthstart, chosenmonthend).filter(category__title=cat_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                incomes__sum.append(income_for_cat.get('Sum'))

                expenditure_for_cat = filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend).filter(category__title=cat_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                expenditures__sum.append(expenditure_for_cat.get('Sum'))
            return JsonResponse(
                data={
                    'labels': titles_of_categories,
                    'incomes__sum': incomes__sum,
                    'expenditures__sum': expenditures__sum,
                })

        elif chart_type == 'tag-sum':
            incomes__sum      = []
            expenditures__sum = []

            tag_objects = Tag.active_objects.all()
            titles_of_tags = list(tag_objects.values_list('title', flat=True))

            for tag_tit in titles_of_tags:
                income_for_tag = filter_objects_by_months(income_objects_of_year, chosenmonthstart, chosenmonthend).filter(tags__title=tag_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                incomes__sum.append(income_for_tag.get('Sum'))

                expenditure_for_tag = filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend).filter(tags__title=tag_tit).aggregate(
                    Sum=Sum('amount', default=0),
                )
                expenditures__sum.append(expenditure_for_tag.get('Sum'))
            return JsonResponse(
                data={
                    'labels': titles_of_tags,
                    'incomes__sum': incomes__sum,
                    'expenditures__sum': expenditures__sum,
                })

    ## --------------------------------

    ## income - year
    income_report__year = income_objects_of_year.aggregate(
        بیشترین=Max('amount', default=0),
        کمترین=Min('amount', default=0),
        میانگین=Avg('amount', default=0, output_field=IntegerField()),
        مجموع=Sum('amount', default=0),
    )
    ## {'sum': 70072153, 'max': 4501840, 'min': 2200, 'avg': 202520.67341040462}

    ## expenditure - year
    expenditure_report__year = expenditure_objects_of_year.aggregate(
        بیشترین=Max('amount', default=0),
        کمترین=Min('amount', default=0),
        میانگین=Avg('amount', default=0, output_field=IntegerField()),
        مجموع=Sum('amount', default=0),
    )



    ## income - month
    income_report__month = filter_objects_by_months(income_objects_of_year, chosenmonthstart, chosenmonthend).aggregate(
        بیشترین=Max('amount', default=0),
        کمترین=Min('amount', default=0),
        میانگین=Avg('amount', default=0, output_field=IntegerField()),
        مجموع=Sum('amount', default=0),
    )
    ## {'sum': 70072153, 'max': 4501840, 'min': 2200, 'avg': 202520.67341040462}

    ## expenditure - month
    expenditure_report__month = filter_objects_by_months(expenditure_objects_of_year, chosenmonthstart, chosenmonthend).aggregate(
        بیشترین=Max('amount', default=0),
        کمترین=Min('amount', default=0),
        میانگین=Avg('amount', default=0, output_field=IntegerField()),
        مجموع=Sum('amount', default=0),
    )

    ## --------------------------------

    return render(
        request,
        f'{APP_SLUG}/reports.html',
        context={
            'main_title': 'گزارش',
            'years': get_years(),
            'names_of_months__persian': NAMES_OF_MONTHS__PERSIAN,

            'chosenyear':       chosenyear,
            'chosenmonthstart': chosenmonthstart,
            'chosenmonthend':   chosenmonthend,

            'chosenmonthstart__zeroed': chosenmonthstart__zeroed,
            'chosenmonthend__zeroed':   chosenmonthend__zeroed,

            'chosenyear__persian':               chosenyear__persian,
            'chosenmonthstart__zeroed__persian': chosenmonthstart__zeroed__persian,
            'chosenmonthend__zeroed__persian':   chosenmonthend__zeroed__persian,

            'expenditure_report__year':  expenditure_report__year,
            'income_report__year':       income_report__year,
            'expenditure_report__month': expenditure_report__month,
            'income_report__month':      income_report__month,
        },
    )

@login_required
def search(request):
    query = get_to_shows(
        request,
        'q',
    )

    matched_transactions = []
    matched_events       = []

    if query:
        try:
            query_int = int(query)  ## 45000
        except ValueError:
            query_int = None

        ## transaction ---------------

        trns_q_objects = Q(short_uuid=query) | \
            Q(title__icontains=query) | \
            Q(slashed_date__icontains=query) | \
            Q(slashed_date_persian__icontains=query) | \
            Q(bank__title__icontains=query) | \
            Q(category__title__icontains=query) | \
            Q(tags__title__icontains=query)

        ## include more fields in search if query is an int
        if query_int is not None:
            trns_q_objects |= Q(amount=query_int) | \
                Q(year=query_int) | \
                Q(month=query_int) | \
                Q(day=query_int) | \
                Q(numerical_date=query_int)

        matched_transactions = get_transaction_objects(request).filter(trns_q_objects).distinct()

        ## event ---------------

        event_q_objects = Q(short_uuid=query) | \
            Q(title__icontains=query) | \
            Q(slashed_date__icontains=query) | \
            Q(slashed_date_persian__icontains=query)

        ## include more fields in search if query is an int
        if query_int is not None:
            event_q_objects |= Q(year=query_int) | \
                Q(month=query_int) | \
                Q(day=query_int) | \
                Q(numerical_date=query_int)

        matched_events = get_event_objects(request).filter(event_q_objects).distinct()

    if all([
        not matched_transactions,
        not matched_events,
    ]):
        return bad_request(request, error_msg='نتیجه‌ای یافت نشد')

    return render(
        request,
        f'{APP_SLUG}/search.html',
        context={
            'main_title': f'جستجو: {query}',
            'matched_transactions': matched_transactions,
            'matched_events': matched_events,
            'queried_string': query,
        }
    )

@login_required
def serve_protected_file(request, path):
    # if not <condition>:
    #     raise Http404()

    file_path = os.path.join(settings.PROTECTED_MEDIA_ROOT, path)

    if not os.path.isfile(file_path):
        raise Http404()

    return FileResponse(open(file_path, 'rb'))
