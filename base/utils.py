from django.contrib import messages
from django.db.models import Q

from jdatetime import datetime
from rahavard import convert_string_True_False_None_0

from .models import (
    Event,
    Transaction,
)


INDEXES_OF_MONTHS = sorted(range(1, 13))

NAMES_OF_MONTHS = [
    'Far', 'Ord', 'Kho', 'Tir', 'Mor', 'Sha',
    'Meh', 'Aba', 'Aza', 'Dey', 'Bah', 'Esf',
]

NAMES_OF_MONTHS__PERSIAN = [
    'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
    'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند',
]


def fahrenheit_to_celsius(fahrenheit):
    if not fahrenheit:
        return 0
    return (fahrenheit - 32) * 5.0 / 9.0

def get_to_shows(request, *args):
    if   request.method == 'GET':  _params_container = request.GET
    elif request.method == 'POST': _params_container = request.POST
    else: return None

    to_shows = []

    for arg in args:
        to_show = convert_string_True_False_None_0(
            _params_container.get(arg, '').strip()
        )

        added = False
        if to_show:
            if arg in [
                'year',
                'month-start',
                'month-end',
            ]:
                try:
                    to_shows.append(abs(int(to_show)))
                    added = True
                except Exception:
                    pass
            else:
                to_shows.append(to_show)
                added = True

        if not added:
            if arg == 'year':
                j_now = datetime.now()
                y = request.COOKIES.get('chosenyear')
                y = convert_string_True_False_None_0(y)
                if not y:
                    to_shows.append(int(j_now.strftime('%Y')))
                else:
                    to_shows.append(int(y))

            elif arg == 'month-start':
                j_now = datetime.now()
                ms = request.COOKIES.get('chosenmonthstart')
                ms = convert_string_True_False_None_0(ms)
                if not ms:
                    to_shows.append(int(j_now.strftime('%m')))
                else:
                    to_shows.append(int(ms))

            elif arg == 'month-end':
                me = request.COOKIES.get('chosenmonthend')
                me = convert_string_True_False_None_0(me)
                if not me:
                    ## month-end is allowed to be left empty
                    ## meaning no need to caluculate current month
                    to_shows.append(0)
                else:
                    to_shows.append(int(me))

    if len(to_shows) == 1:
        return to_shows[0]

    return to_shows

def get_years():
    return sorted(set(Transaction.active_objects.values_list('year', flat=True)))

def get_transaction_objects(request):
    return Transaction.active_objects.filter(author=request.user)

def get_event_objects(request):
    return Event.active_objects.filter(author=request.user)

def filter_objects_by_months(objects, chosenmonthstart, chosenmonthend):
    if chosenmonthend:
        return objects.filter(
            Q(month__gte=chosenmonthstart) &
            Q(month__lte=chosenmonthend),
        )

    return objects.filter(
        month=chosenmonthstart,
    )

def split_into_year_month_day(date_string, separate_by):
    '''
        1402-01-14 -> [1402, 1, 14]
        1402/01/14 -> [1402, 1, 14]
    '''
    return list(
        map(
            lambda _: int(_),
            date_string.split(separate_by)
        )
    )
